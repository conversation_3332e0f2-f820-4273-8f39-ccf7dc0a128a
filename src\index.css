@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 245 13% 9%;
    --foreground: 0 0% 98%;

    --card: 245 13% 11%;
    --card-foreground: 0 0% 98%;

    --popover: 245 13% 11%;
    --popover-foreground: 0 0% 98%;

    --primary: 265 100% 70%;
    --primary-foreground: 0 0% 98%;

    --secondary: 245 13% 15%;
    --secondary-foreground: 0 0% 98%;

    --muted: 245 13% 15%;
    --muted-foreground: 240 5% 65%;

    --accent: 130 90% 50%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 245 13% 15%;
    --input: 245 13% 15%;
    --ring: 265 100% 70%;

    --radius: 0.75rem;
    
    /* Solana Pay Design System */
    --solana-purple: 265 100% 70%;
    --solana-green: 130 90% 50%;
    --solana-dark: 245 13% 9%;
    --solana-card: 245 13% 11%;
    --solana-border: 245 13% 15%;
    
    /* Gradients */
    --gradient-solana: linear-gradient(135deg, hsl(var(--solana-purple)), hsl(var(--solana-green)));
    --gradient-subtle: linear-gradient(135deg, hsl(var(--solana-card)), hsl(var(--solana-border)));
    --gradient-hero: linear-gradient(135deg, hsl(265 100% 70% / 0.9), hsl(130 90% 50% / 0.9));
    
    /* Shadows */
    --shadow-solana: 0 10px 40px hsl(var(--solana-purple) / 0.2);
    --shadow-glow: 0 0 40px hsl(var(--solana-purple) / 0.15);
    --shadow-card: 0 4px 20px hsl(245 13% 5% / 0.4);
    
    /* Animation */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .light {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 265 100% 70%;
    --primary-foreground: 0 0% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 130 90% 50%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 265 100% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}